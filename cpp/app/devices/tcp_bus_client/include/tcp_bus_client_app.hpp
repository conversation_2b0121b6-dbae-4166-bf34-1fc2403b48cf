/**
 * @file tcp_bus_client_app.hpp
 * @brief TCP总线客户端应用程序主类定义
 * <AUTHOR>
 * @date 2025-08-25
 */

#ifndef TCP_BUS_CLIENT_APP_HPP
#define TCP_BUS_CLIENT_APP_HPP

#include "../../include/base_client_app.hpp"
#include "type1_processor.hpp"
#include "type2_processor.hpp"
#include "event_reporter.hpp"

namespace tcp_bus_client {

/**
 * @brief TCP总线客户端应用程序主类
 *
 * 继承自BaseClientApp，实现具体的TCP总线客户端功能
 */
class TcpBusClientApp : public devices::BaseClientApp {
public:
    /**
     * @brief 构造函数
     * @param event_loop 事件循环引用
     * @param server_host 服务器地址
     * @param server_port 服务器端口
     * @param client_name 客户端名称
     * @param client_id 客户端ID
     */
    TcpBusClientApp(zexuan::net::EventLoop& event_loop,
                    const std::string& server_host,
                    uint16_t server_port,
                    const std::string& client_name,
                    int client_id = 9)
        : devices::BaseClientApp(event_loop, server_host, server_port, client_name, client_id) {}

protected:
    /**
     * @brief 初始化处理器和上报器
     * @return true表示初始化成功，false表示初始化失败
     */
    bool initialize() override {
        // 初始化消息处理器
        addProcessor(std::make_unique<Type1Processor>(getClient()));
        addProcessor(std::make_unique<Type2Processor>(getClient(), getEventLoop()));

        // 初始化事件上报器
        setEventReporter(std::make_unique<EventReporter>(getClient(), getEventLoop()));

        spdlog::info("Initialized TCP bus client processors and event reporter");
        return true;
    }

    /**
     * @brief 获取订阅的消息类型
     * @return 消息类型列表
     */
    std::vector<int> getSubscribedMessageTypes() const override {
        std::vector<int> message_types;
        // 订阅所有CommonMessage类型 (假设类型1-10)
        for (int i = 1; i <= 10; ++i) {
            message_types.push_back(i);
        }
        return message_types;
    }

    /**
     * @brief 获取订阅的事件类型
     * @return 事件类型列表
     */
    std::vector<int> getSubscribedEventTypes() const override {
        // 订阅统一的事件类型1
        return {1};
    }


};

} // namespace tcp_bus_client

#endif // TCP_BUS_CLIENT_APP_HPP
