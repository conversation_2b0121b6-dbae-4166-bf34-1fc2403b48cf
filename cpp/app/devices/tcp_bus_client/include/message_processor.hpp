/**
 * @file message_processor.hpp
 * @brief 消息处理器基类定义
 * <AUTHOR>
 * @date 2025-08-25
 */

#ifndef TCP_BUS_CLIENT_MESSAGE_PROCESSOR_HPP
#define TCP_BUS_CLIENT_MESSAGE_PROCESSOR_HPP

#include "../../include/base_message_processor.hpp"

namespace tcp_bus_client {

/**
 * @brief 抽象消息处理器基类
 *
 * 继承自BaseMessageProcessor，定义了处理IEC103消息的通用接口
 */
class MessageProcessor : public devices::BaseMessageProcessor {
public:
    /**
     * @brief 构造函数
     * @param client TCP总线客户端引用
     */
    explicit MessageProcessor(zexuan::bus::TcpBusClient& client) : devices::BaseMessageProcessor(client) {}

    /**
     * @brief 虚析构函数
     */
    virtual ~MessageProcessor() = default;

protected:
    /**
     * @brief 创建IEC103响应消息
     * @param input_msg 输入的IEC103消息
     * @param cot 传送原因
     * @param content 响应内容
     * @return 创建的IEC103响应消息
     */
    zexuan::base::Message createIEC103Response(const zexuan::base::Message& input_msg,
                                              uint8_t cot,
                                              const std::string& content) const {
        zexuan::base::Message response_msg;
        response_msg.setTyp(input_msg.getTyp());
        response_msg.setVsq(input_msg.getVsq());
        response_msg.setCot(cot);
        response_msg.setSource(input_msg.getTarget());
        response_msg.setTarget(input_msg.getSource());
        response_msg.setFun(input_msg.getFun());
        response_msg.setInf(input_msg.getInf());
        response_msg.setTextContent(content);
        return response_msg;
    }
};

} // namespace tcp_bus_client

#endif // TCP_BUS_CLIENT_MESSAGE_PROCESSOR_HPP
