/**
 * @file base_client_app.hpp
 * @brief TCP总线客户端应用程序基类定义
 * <AUTHOR>
 * @date 2025-08-25
 */

#ifndef DEVICES_BASE_CLIENT_APP_HPP
#define DEVICES_BASE_CLIENT_APP_HPP

#include "base_message_processor.hpp"
#include "base_event_reporter.hpp"
#include "zexuan/bus/tcp_bus_client.hpp"
#include "zexuan/net/event_loop.hpp"
#include "zexuan/base/message.hpp"
#include "zexuan/base/types/structs.hpp"
#include <memory>
#include <vector>
#include <atomic>
#include <spdlog/spdlog.h>

namespace devices {

/**
 * @brief TCP总线客户端应用程序基类
 * 
 * 提供通用的客户端应用程序框架，子类需要实现具体的初始化和消息处理逻辑
 */
class BaseClientApp {
public:
    /**
     * @brief 构造函数
     * @param event_loop 事件循环引用
     * @param server_host 服务器地址
     * @param server_port 服务器端口
     * @param client_name 客户端名称
     * @param client_id 客户端ID
     */
    BaseClientApp(zexuan::net::EventLoop& event_loop,
                  const std::string& server_host,
                  uint16_t server_port,
                  const std::string& client_name,
                  int client_id)
        : event_loop_(event_loop),
          client_(std::make_unique<zexuan::bus::TcpBusClient>(&event_loop, server_host, server_port, client_name)),
          running_(false) {
        
        // 设置客户端ID
        client_->setClientId(client_id);
        
        // 设置回调函数
        client_->setCommonMessageCallback([this](const zexuan::base::CommonMessage& msg) {
            onCommonMessage(msg);
        });
        
        client_->setEventMessageCallback([this](const zexuan::base::EventMessage& msg) {
            onEventMessage(msg);
        });
        
        client_->setControlMessageCallback([this](const zexuan::base::ControlMessage& msg) {
            onControlMessage(msg);
        });
    }

    /**
     * @brief 虚析构函数
     */
    virtual ~BaseClientApp() {
        stop();
    }

    /**
     * @brief 启动应用程序
     * @return true表示启动成功，false表示启动失败
     */
    virtual bool start() {
        if (running_.load()) {
            spdlog::warn("Client app is already running");
            return false;
        }

        spdlog::info("Starting client app...");
        
        // 初始化处理器和上报器
        if (!initialize()) {
            spdlog::error("Failed to initialize client app");
            return false;
        }
        
        // 启动连接
        client_->connect();
        running_.store(true);
        
        // 设置连接检查和订阅
        setupConnectionAndSubscription();
        
        return true;
    }

    /**
     * @brief 停止应用程序
     */
    virtual void stop() {
        if (!running_.load()) {
            return;
        }

        spdlog::info("Stopping client app...");
        running_.store(false);
        
        // 停止事件上报
        if (event_reporter_) {
            event_reporter_->stop();
        }
        
        // 断开连接
        if (client_) {
            client_->disconnect();
        }
        
        // 清理资源
        cleanup();
        
        spdlog::info("Client app stopped");
    }

    /**
     * @brief 检查是否正在运行
     * @return true表示正在运行，false表示已停止
     */
    bool isRunning() const {
        return running_.load();
    }

    /**
     * @brief 获取客户端引用
     * @return TCP总线客户端引用
     */
    zexuan::bus::TcpBusClient& getClient() {
        return *client_;
    }

protected:
    /**
     * @brief 初始化处理器和上报器（纯虚函数，子类必须实现）
     * @return true表示初始化成功，false表示初始化失败
     */
    virtual bool initialize() = 0;

    /**
     * @brief 清理资源（虚函数，子类可以重写）
     */
    virtual void cleanup() {}

    /**
     * @brief 获取订阅的消息类型（纯虚函数，子类必须实现）
     * @return 消息类型列表
     */
    virtual std::vector<int> getSubscribedMessageTypes() const = 0;

    /**
     * @brief 获取订阅的事件类型（纯虚函数，子类必须实现）
     * @return 事件类型列表
     */
    virtual std::vector<int> getSubscribedEventTypes() const = 0;

    /**
     * @brief 处理业务消息（虚函数，子类可以重写）
     * @param original_message 原始消息
     */
    virtual void processBusinessMessage(const zexuan::base::CommonMessage& original_message) {
        spdlog::debug("Processing business message: invoke_id={}", original_message.invoke_id);

        // 尝试解析输入的消息
        zexuan::base::Message input_msg;
        size_t parsed = input_msg.deserialize(original_message.data);

        if (parsed > 0) {
            // 成功解析消息
            spdlog::debug("Parsed input message: TYP={:02X}, COT={:02X}, FUN={:02X}, INF={:02X}",
                         input_msg.getTyp(), input_msg.getVsq(), input_msg.getCot(), input_msg.getFun(),
                         input_msg.getInf());

            // 查找合适的处理器
            bool processed = false;
            for (auto& processor : processors_) {
                if (processor->canProcess(input_msg.getTyp())) {
                    spdlog::debug("Using processor: {}", processor->getName());
                    processed = processor->processMessage(original_message, input_msg);
                    break;
                }
            }

            if (!processed) {
                // 没有找到合适的处理器，发送简单响应
                spdlog::debug("No suitable processor found, sending simple response");
                sendSimpleResponse(original_message);
            }
        } else {
            // 不能解析，生成简单响应
            sendSimpleResponse(original_message);
        }
    }

    /**
     * @brief 添加消息处理器
     * @param processor 消息处理器
     */
    void addProcessor(std::unique_ptr<BaseMessageProcessor> processor) {
        processors_.push_back(std::move(processor));
    }

    /**
     * @brief 设置事件上报器
     * @param reporter 事件上报器
     */
    void setEventReporter(std::unique_ptr<BaseEventReporter> reporter) {
        event_reporter_ = std::move(reporter);
    }

    /**
     * @brief 获取事件循环引用
     * @return 事件循环引用
     */
    zexuan::net::EventLoop& getEventLoop() { return event_loop_; }

private:
    /**
     * @brief 设置连接检查和订阅
     */
    void setupConnectionAndSubscription() {
        // 使用成员函数来避免递归lambda的内存泄漏问题
        checkConnectionAndSubscribe();

        // 设置连接超时检查
        event_loop_.runAfter(10.0, [this]() {
            if (!client_->isConnected()) {
                spdlog::error("Failed to connect to server after 10 seconds");
                stop();
                event_loop_.quit();
            }
        });
    }

    /**
     * @brief 检查连接状态并订阅
     */
    void checkConnectionAndSubscribe() {
        if (!running_.load()) {
            return;
        }

        if (!client_->isConnected()) {
            // 每100ms检查一次连接状态
            event_loop_.runAfter(0.1, [this]() {
                checkConnectionAndSubscribe();
            });
            return;
        }

        spdlog::info("Connected to server successfully");

        // 获取订阅类型
        std::vector<int> message_types = getSubscribedMessageTypes();
        std::vector<int> event_types = getSubscribedEventTypes();

        bool subscribe_result = client_->subscribe(message_types, event_types);
        spdlog::info("Subscribe result: {}", subscribe_result);

        if (subscribe_result) {
            spdlog::info("Sent subscription request");

            // 启动事件上报
            if (event_reporter_) {
                event_reporter_->start();
                spdlog::info("Started automatic event reporting");
            }
        } else {
            spdlog::error("Failed to send subscription request");
        }
    }

    /**
     * @brief CommonMessage回调处理
     */
    void onCommonMessage(const zexuan::base::CommonMessage& msg) {
        spdlog::info("Received CommonMessage: type={}, source_id={}, target_id={}, invoke_id={}, data_size={}",
                    static_cast<int>(msg.type), msg.source_id, msg.target_id, msg.invoke_id, msg.data.size());

        handleReceivedMessage(msg);
    }

    /**
     * @brief EventMessage回调处理
     */
    void onEventMessage(const zexuan::base::EventMessage& msg) {
        spdlog::info("Received EventMessage: event_type={}, device_id={}, source_id={}, description={}, data_size={}",
                    msg.event_type, msg.device_uuid.device_id, msg.source_id, msg.description, msg.data.size());
        
        // EventMessage暂时不处理，只记录
        spdlog::debug("EventMessage received but not processed");
    }

    /**
     * @brief ControlMessage回调处理
     */
    void onControlMessage(const zexuan::base::ControlMessage& msg) {
        spdlog::info("Received ControlMessage: action={}, success={}, message={}", 
                    msg.action, msg.success, msg.message);
        
        if (msg.action == "subscribe_response") {
            if (msg.success) {
                spdlog::info("Subscription successful. Subscribed message types: {}, event types: {}", 
                            msg.subscribed_message_types.size(), msg.subscribed_event_types.size());
            } else {
                spdlog::error("Subscription failed: {}", msg.message);
            }
        }
    }

    /**
     * @brief 处理接收到的消息
     */
    void handleReceivedMessage(const zexuan::base::CommonMessage& message) {
        spdlog::info("Processing CommonMessage: type={}, source_id={}, invoke_id={}",
                    static_cast<int>(message.type), message.source_id, message.invoke_id);

        // 只处理来自ProtocolService的消息（避免处理自己发送的消息）
        if (message.source_id == zexuan::base::SERVICE_SUBJECT_ID) {  // SERVICE_SUBJECT_ID = 2050
            processBusinessMessage(message);
        } else {
            spdlog::debug("Ignoring message not from ProtocolService (source_id={}, expected={})", 
                         message.source_id, zexuan::base::SERVICE_SUBJECT_ID);
        }
    }

    /**
     * @brief 发送简单响应
     */
    void sendSimpleResponse(const zexuan::base::CommonMessage& original_message) {
        spdlog::debug("Sending simple response for message");

        zexuan::base::CommonMessage response;
        response.type = zexuan::base::MessageType::RESULT;
        response.source_id = client_->getClientId();
        response.target_id = original_message.source_id;
        response.invoke_id = original_message.invoke_id;
        response.b_lastmsg = true;

        // 生成简单响应
        std::string simple_response = "SIMPLE_RESPONSE_FOR_" + original_message.invoke_id;
        response.data.assign(simple_response.begin(), simple_response.end());

        // 发送响应到总线
        if (client_->sendCommonMessage(response)) {
            spdlog::debug("Sent simple response to bus: invoke_id={}", response.invoke_id);
        } else {
            spdlog::error("Failed to send simple response to bus: invoke_id={}", response.invoke_id);
        }
    }

protected:
    zexuan::net::EventLoop& event_loop_;                                    ///< 事件循环引用
    std::unique_ptr<zexuan::bus::TcpBusClient> client_;                    ///< TCP总线客户端
    std::vector<std::unique_ptr<BaseMessageProcessor>> processors_;        ///< 消息处理器列表
    std::unique_ptr<BaseEventReporter> event_reporter_;                    ///< 事件上报器
    std::atomic<bool> running_;                                            ///< 运行状态标志
};

} // namespace devices

#endif // DEVICES_BASE_CLIENT_APP_HPP
